import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Badge } from '@instructure/ui-badge'
import { Grid } from '@instructure/ui-grid'
import { IconGroupLine, IconClockLine, IconDashboardLine, IconDocumentLine, IconSettingsLine } from '@instructure/ui-icons'
import type { ConsultationRequest, ConsultationStatistics } from '../types'

interface FacultyConsultationsProps {
  currentUserId: string
  pendingRequests: ConsultationRequest[]
  upcomingConsultations: ConsultationRequest[]
  statistics: ConsultationStatistics
}

const FacultyConsultations: React.FC<FacultyConsultationsProps> = ({
  currentUserId,
  pendingRequests,
  upcomingConsultations,
  statistics
}) => {
  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getTimeUntilConsultation = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      const diffMs = consultationTime.getTime() - now.getTime()
      
      if (diffMs < 0) {
        return 'Past due'
      }
      
      const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
      const diffDays = Math.floor(diffHours / 24)
      
      if (diffDays > 0) {
        return `In ${diffDays} day${diffDays !== 1 ? 's' : ''}`
      } else if (diffHours > 0) {
        return `In ${diffHours} hour${diffHours !== 1 ? 's' : ''}`
      } else {
        const diffMinutes = Math.floor(diffMs / (1000 * 60))
        return `In ${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''}`
      }
    } catch {
      return ''
    }
  }

  const isConsultationSoon = (dateTimeString: string) => {
    try {
      const consultationTime = new Date(dateTimeString)
      const now = new Date()
      const diffMs = consultationTime.getTime() - now.getTime()
      const diffHours = diffMs / (1000 * 60 * 60)
      
      return diffHours <= 24 && diffHours > 0
    } catch {
      return false
    }
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconGroupLine /> Faculty Consultations
          </Heading>
          <p>Manage your consultation requests, time slots, and view upcoming appointments with students.</p>
        </div>

        {/* Quick Stats */}
        <Grid>
          <Grid.Row>
            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="alert">
                  {pendingRequests.length}
                </Text>
                <Text size="small" color="secondary">
                  Pending Requests
                </Text>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="success">
                  {upcomingConsultations.length}
                </Text>
                <Text size="small" color="secondary">
                  Upcoming
                </Text>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="brand">
                  {statistics.total_consultations || 0}
                </Text>
                <Text size="small" color="secondary">
                  Total Consultations
                </Text>
              </View>
            </Grid.Col>

            <Grid.Col width={3}>
              <View as="div" background="primary" padding="medium" borderRadius="medium" textAlign="center">
                <Text size="xx-large" weight="bold" color="brand">
                  {Math.round(statistics.average_per_month || 0)}
                </Text>
                <Text size="small" color="secondary">
                  Average per Month
                </Text>
              </View>
            </Grid.Col>
          </Grid.Row>
        </Grid>

        {/* Quick Actions */}
        <View as="div" margin="large 0 0 0">
          <Heading level="h2" margin="0 0 medium 0">
            Quick Actions
          </Heading>
          <Grid>
            <Grid.Row>
              <Grid.Col width={4}>
                <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
                  <View as="div" display="flex" margin="0 0 small 0">
                    <IconDashboardLine size="medium" />
                    <Heading level="h4" margin="0 0 0 small">
                      Dashboard
                    </Heading>
                    {pendingRequests.length > 0 && (
                      <Badge 
                        count={pendingRequests.length}
                        margin="0 0 0 small"
                        type="notification"
                      />
                    )}
                  </View>
                  <View as="div" margin="small 0 small 0">
                    <Button 
                      size="small"
                      href="/consultation_requests/faculty_dashboard"
                      renderIcon={IconDashboardLine}
                    >
                      Dashboard
                    </Button>
                  </View>
                  <Text size="small">
                    Review pending requests and manage upcoming consultations.
                  </Text>
                </View>
              </Grid.Col>
              
              <Grid.Col width={4}>
                <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
                  <View as="div" display="flex" margin="0 0 small 0">
                    <IconClockLine size="medium" />
                    <Heading level="h4" margin="0 0 0 small">
                      Time Slots
                    </Heading>
                  </View>
                  <View as="div" margin="small 0 small 0">
                    <Button 
                      size="small"
                      href="/faculty_time_slots"
                      renderIcon={IconClockLine}
                    >
                      Manage Slots
                    </Button>
                  </View>
                  <Text size="small">
                    Manage your available consultation time slots.
                  </Text>
                </View>
              </Grid.Col>
              
              <Grid.Col width={4}>
                <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
                  <View as="div" display="flex" margin="0 0 small 0">
                    <IconDocumentLine size="medium" />
                    <Heading level="h4" margin="0 0 0 small">
                      Summaries
                    </Heading>
                  </View>
                  <View as="div" margin="small 0 small 0">
                    <Button 
                      size="small"
                      href="/consultation_summaries"
                      renderIcon={IconDocumentLine}
                    >
                      View Summaries
                    </Button>
                  </View>
                  <Text size="small">
                    View and manage consultation summaries and reports.
                  </Text>
                </View>
              </Grid.Col>
            </Grid.Row>
          </Grid>
        </View>

        {/* Pending Requests */}
        {pendingRequests.length > 0 && (
          <View as="div" margin="large 0 0 0">
            <Heading level="h2" margin="0 0 medium 0">
              Pending Requests ({pendingRequests.length})
            </Heading>
            
            <View as="div">
              {pendingRequests.slice(0, 3).map(request => (
                <View
                  key={request.id}
                  as="div"
                  background="primary"
                  padding="medium"
                  borderRadius="medium"
                  borderWidth="small"
                  borderColor="warning"
                  margin="0 0 small 0"
                >
                  <View as="div" display="flex" style={{ justifyContent: 'space-between' }} id='request-item 1'>
                    <View as="div">
                      <View as="div" display="flex" margin="0 0 x-small 0">
                        <Text weight="bold">
                          {request.student_name}
                        </Text>
                        <Text size="small" color="secondary" style={{ margin: '0 0 0 5' }}>
                          ({request.student_id})
                        </Text>
                      </View>
                      <View as="div" display="flex" margin="0 0 x-small 0">
                        <IconClockLine size="x-small" />
                        <Text size="small" style={{ margin: '0 0 0 5' }}>
                          {formatDateTime(request.preferred_datetime)}
                        </Text>
                      </View>
                      <Text size="small" color="secondary">
                        {request.concern_type_display}
                      </Text>
                    </View>
                    <View as="div">
                      <Button
                        size="small"
                        color="primary"
                        href={`/consultation_requests/faculty_dashboard?highlight=${request.id}`}
                      >
                        Review 2
                      </Button>
                    </View>
                  </View>
                </View>
              ))}
              
              {pendingRequests.length > 3 && (
                <View as="div" textAlign="center" margin="medium 0 0 0">
                  <Button href="/consultation_requests/faculty_dashboard">
                    View All {pendingRequests.length} Pending Requests
                  </Button>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Upcoming Consultations */}
        {upcomingConsultations.length > 0 && (
          <View as="div" margin="large 0 0 0">
            <Heading level="h2" margin="0 0 medium 0">
              Upcoming Consultations ({upcomingConsultations.length})
            </Heading>
            
            <View as="div">
              {upcomingConsultations.slice(0, 3).map(consultation => (
                <View
                  key={consultation.id}
                  as="div"
                  background="primary"
                  padding="medium"
                  borderRadius="medium"
                  borderWidth="small"
                  borderColor={isConsultationSoon(consultation.preferred_datetime) ? 'warning' : 'success'}
                  margin="0 0 small 0"
                >
                  <View as="div" display="flex" style={{ justifyContent: 'space-between' }} id='request-item 2'>
                    <View as="div">
                      <View as="div" display="flex" margin="0 0 x-small 0">
                        <Text weight="bold">
                          {consultation.student_name}
                        </Text>
                        <Text size="small" color="secondary" style={{ margin: '0 0 0 5' }}>
                          ({consultation.student_id})
                        </Text>
                        {isConsultationSoon(consultation.preferred_datetime) && (
                          <Badge
                            type="notification"
                            margin="0 0 0 small"
                          />
                        )}
                      </View>
                      <View as="div" display="flex" margin="0 0 x-small 0">
                        <IconClockLine size="x-small" />
                        <Text size="small" style={{ margin: '0 0 0 5' }}>
                          {formatDateTime(consultation.preferred_datetime)}
                        </Text>
                        <Text size="small" color="secondary" style={{ margin: '0 0 0 5' }}>
                          ({getTimeUntilConsultation(consultation.preferred_datetime)})
                        </Text>
                      </View>
                      <Text size="small" color="secondary">
                        {consultation.concern_type_display}
                      </Text>
                    </View>
                    <View as="div">
                      <Button
                        size="small"
                        href={`/calendar?event_id=${consultation.id}`}
                        target="_blank"
                      >
                        View in Calendar 2
                      </Button>
                    </View>
                  </View>
                </View>
              ))}
              
              {upcomingConsultations.length > 3 && (
                <View as="div" textAlign="center" margin="medium 0 0 0">
                  <Button href="/consultation_requests/faculty_dashboard">
                    View All {upcomingConsultations.length} Upcoming Consultations
                  </Button>
                </View>
              )}
            </View>
          </View>
        )}

        {/* Help Section */}
        <View as="div" margin="large 0 0 0" background="secondary" padding="medium" borderRadius="medium">
          <Heading level="h4" margin="0 0 small 0">
            Faculty Resources
          </Heading>
          <Text>
            For guidance on conducting effective consultations or technical support with the system, 
            please refer to the faculty handbook or contact the IT Help Desk.
          </Text>
        </View>
      </View>
    </div>
  )
}

export default FacultyConsultations
